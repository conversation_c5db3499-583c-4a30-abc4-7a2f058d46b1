<template>
  <div class="cashflow-page">
    <div class="page-header">
      <h1>现金流量分析</h1>
      <div class="header-actions">
      </div>
    </div>

    <div class="page-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>现金流量表</h3>
            <el-dropdown @command="handleTableTypeChange" trigger="click">
              <el-button type="primary" size="small">
                {{ currentTableTypeLabel }}
                <el-icon class="el-icon--right">
                  <arrow-down/>
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="all">全部</el-dropdown-item>
                  <el-dropdown-item command="cashInflow">现金流入</el-dropdown-item>
                  <el-dropdown-item command="cashOutflow">现金流出</el-dropdown-item>
                  <el-dropdown-item command="NetCashFlow">税务计算</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>

        <div v-loading="loading" element-loading-text="加载数据中...">
          <div class="table-container">
            <el-table
                :data="currentTableData"
                border
                stripe
                class="cashflow-table"
                :row-class-name="getRowClassName"
            >
              <el-table-column
                  prop="项目名称"
                  label="项目名称"
                  width="200"
                  fixed="left"
                  align="left"
              />
              <el-table-column
                  prop="合计"
                  label="合计"
                  width="150"
                  align="right"
              />
              <el-table-column
                  v-if="hasConstructionPeriod"
                  label="建设期"
                  align="center"
              >
                <el-table-column prop="-2" label="-2" width="120" align="right"  />
                <el-table-column prop="-1" label="-1" width="120" align="right"/>
              </el-table-column>
              <el-table-column label="运营期" align="center">
                <el-table-column
                    v-for="year in yearColumns"
                    :key="`year-${year}`"
                    :prop="year.toString()"
                    :label="year.toString()"
                    width="120"
                    align="right"
                />
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {ElMessage} from 'element-plus'
import {ArrowDown} from '@element-plus/icons-vue'
import {useProjectStore} from '@/store/modules/project'
import {
  adaptCashFlowData,
  getYearColumns,
  hasConstructionPeriod as checkConstructionPeriod
} from '@/utils/cashFlowAdapter'

// 使用 Pinia store
const projectStore = useProjectStore()

// 响应式状态
const loading = ref(false)
const currentTableType = ref('all')

// 表格类型标签映射
const tableTypeLabels = {
  all: '全部',
  cashInflow: '现金流入',
  cashOutflow: '现金流出',
  NetCashFlow: '税务计算'
}

// 当前表格类型标签
const currentTableTypeLabel = computed(() => {
  return tableTypeLabels[currentTableType.value] || '全部'
})

// 适配后的现金流数据
const adaptedCashFlowData = computed(() => {
  const cashFlowData = projectStore.currentProject?.cash_flow || []
  return adaptCashFlowData(cashFlowData)
})

// 当前表格数据
const currentTableData = computed(() => {
  return adaptedCashFlowData.value[currentTableType.value] || []
})

// 年份列
const yearColumns = computed(() => {
  const cashFlowData = projectStore.currentProject?.cash_flow || []
  return getYearColumns(cashFlowData)
})

// 是否有建设期数据
const hasConstructionPeriod = computed(() => {
  const cashFlowData = projectStore.currentProject?.cash_flow || []
  return checkConstructionPeriod(cashFlowData)
})

// 处理表格类型切换
const handleTableTypeChange = (command) => {
  currentTableType.value = command
  ElMessage.success(`已切换到${tableTypeLabels[command]}`)
}

// 加载演示数据
// 获取行的CSS类名
const getRowClassName = ({row, rowIndex}) => {
  if (row.isTotal) {
    return 'total-row'
  }
  if (row.isSpacer) {
    return 'spacer-row'
  }
  return ''
}

// 初始化数据加载
const initializeData = async () => {
  loading.value = true
  try {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('请求超时'))
      }, 2000)
    })
    const checkProject = new Promise((resolve) => {
          const checkIt = () => {
            if (projectStore.currentProject) {
              resolve("获取项目数据成功")
            } else {
              setTimeout(checkIt, 50)
            }
          }
          checkIt()
        }
    )
    await Promise.race([timeoutPromise, checkProject])
    ElMessage.success("数据加载成功")
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  initializeData()
})

</script>

<style scoped>
.cashflow-page {
  padding: 10px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  color: #303133;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.page-content {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.table-container {
  overflow-x: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.cashflow-table {
  min-width: max-content;
}

.cashflow-table :deep(.el-table__header th) {
  background-color: #f8f9fa !important;
  color: #303133;
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid #e4e7ed;
}

.cashflow-table :deep(.el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

.cashflow-table :deep(.el-table__row--striped td) {
  background-color: #fafafa;
}

.cashflow-table :deep(.el-table__body td) {
  padding: 8px 12px;
  font-size: 13px;
}

.cashflow-table :deep(.el-table__body td:first-child) {
  font-weight: 500;
}

.cashflow-table :deep(.el-table__fixed-left) {
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

/* 合计行样式 */
.cashflow-table :deep(.total-row td) {
  background-color: #e8f5e8 !important;
  font-weight: bold;
  color: #2d5a2d;
  border-top: 2px solid #67c23a;
}

/* 分隔行样式 */
.cashflow-table :deep(.spacer-row td) {
  background-color: #f8f9fa !important;
  height: 20px;
  padding: 0 !important;
  border: none !important;
}

.cashflow-table :deep(.spacer-row td .cell) {
  height: 20px;
  line-height: 20px;
}

.page-content :deep(.el-card__body) {
  padding: 10px;
}

@media (max-width: 768px) {
  .cashflow-page {
    padding: 8px;
  }

  .table-container {
    font-size: 12px;
  }
}
</style>
