<template>
	<div class="export-preview-page">
		<div class="preview-header">
			<div class="header-left">
				<h1>导出报告</h1>
				<!-- 项目选择 -->
				<div class="project-selector">
					<el-dropdown @command="handleProjectSelect" trigger="click">
						<el-button type="primary" plain>
							<el-icon><folder /></el-icon>
							{{ selectedProject?.name || '选择项目' }}
							<el-icon class="el-icon--right"><arrow-down /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item disabled style="color: #909399; font-weight: bold"> 历史项目记录 </el-dropdown-item>
								<el-dropdown-item
									v-for="project in projectHistory"
									:key="project.id"
									:command="project.id"
									:class="{ 'is-active': selectedProject?.id === project.id }"
								>
									<div class="project-item">
										<div class="project-name">{{ project.name }}</div>
										<div class="project-time">{{ formatTime(project.updatedAt) }}</div>
									</div>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</div>

			<div class="header-actions">
				<el-button @click="refreshPreview" :loading="loading">
					<el-icon><refresh /></el-icon>
					刷新预览
				</el-button>
				<el-button type="primary" @click="downloadExcel" :loading="downloading">
					<el-icon><download /></el-icon>
					下载Excel文件
				</el-button>
				<el-button @click="closeWindow">
					<el-icon><close /></el-icon>
					关闭
				</el-button>
			</div>
		</div>

		<div class="preview-content" v-loading="loading">
			<!-- Sheet标签页 -->
			<el-tabs v-model="activeSheet" type="card" class="sheet-tabs">
				<el-tab-pane label="能源项目测算" name="energy">
					<div class="sheet-content">
						<h3>光伏参数</h3>
						<div class="preview-table-container">
							<el-table :data="photovoltaicData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="150" align="center" />
								<el-table-column prop="unit" label="单位" width="100" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" s />
							</el-table>
						</div>
					</div>
					<div class="sheet-content">
						<h3>风电参数</h3>
						<div class="preview-table-container">
							<el-table :data="windPowerData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="150" align="center" />
								<el-table-column prop="unit" label="单位" width="100" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" />
							</el-table>
						</div>
					</div>
					<div class="sheet-content">
						<h3>电网系统</h3>
						<div class="preview-table-container">
							<el-table :data="gridSystemData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="150" align="center" />
								<el-table-column prop="unit" label="单位" width="100" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" />
							</el-table>
						</div>
					</div>
					<div class="sheet-content">
						<h3>制氢厂参数</h3>
						<div class="preview-table-container">
							<el-table :data="hydrogenPlantData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="150" align="center" />
								<el-table-column prop="unit" label="单位" width="100" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" />
							</el-table>
						</div>
					</div>
					<div class="sheet-content">
						<h3>融资贷款</h3>
						<div class="preview-table-container">
							<el-table :data="financingData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="150" align="center" />
								<el-table-column prop="unit" label="单位" width="100" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" />
							</el-table>
						</div>
					</div>
					<div class="sheet-content">
						<h3>液氨参数</h3>
						<div class="preview-table-container">
							<el-table :data="liquidAmmoniaData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="150" align="center" />
								<el-table-column prop="unit" label="单位" width="100" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" />
							</el-table>
						</div>
					</div>
				</el-tab-pane>

				<el-tab-pane label="产品方案" name="solution">
					<div class="sheet-content">
						<h3>产品方案配置</h3>
						<div class="preview-table-container">
							<el-table :data="solutionData" border stripe>
								<el-table-column prop="parameter" label="参数名称" width="200" align="center" />
								<el-table-column prop="value" label="数值" width="120" align="center" />
								<el-table-column prop="unit" label="单位" width="80" align="center" />
								<el-table-column prop="category" label="类别" width="120" align="center" />
								<el-table-column prop="remark" label="备注" align="left" />
							</el-table>
						</div>
					</div>
				</el-tab-pane>

				<el-tab-pane label="现金流量表" name="CashFlow">
					<div class="sheet-content">
						<!-- 现金流入 -->
						<div class="cash-flow-section">
							<h3>现金流入</h3>
							<div class="preview-table-container">
								<el-table :data="CashFlowInflowData" border stripe class="CashFlow-table">
									<el-table-column prop="项目名称" label="项目名称" width="200" align="center" fixed="left" />
									<el-table-column prop="合计" label="合计" width="120" align="center" />
									<el-table-column label="建设期" align="center">
										<el-table-column prop="-2" label="-2" width="100" align="center" />
										<el-table-column prop="-1" label="-1" width="100" align="center" />
									</el-table-column>
									<el-table-column label="运营期(年)" align="center">
										<el-table-column
											v-for="year in allYears"
											:key="year"
											:prop="year.toString()"
											:label="year.toString()"
											width="100"
											align="center"
										/>
									</el-table-column>
								</el-table>
							</div>
						</div>

						<!-- 现金流出 -->
						<div class="cash-flow-section">
							<h3>现金流出</h3>
							<div class="preview-table-container">
								<el-table :data="CashFlowOutflowData" border stripe class="CashFlow-table">
									<el-table-column prop="项目名称" label="项目名称" width="200" align="center" fixed="left" />
									<el-table-column prop="合计" label="合计" width="120" align="center" />
									<el-table-column label="建设期" align="center">
										<el-table-column prop="-2" label="-2" width="100" align="center" />
										<el-table-column prop="-1" label="-1" width="100" align="center" />
									</el-table-column>
									<el-table-column label="运营期(年)" align="center">
										<el-table-column
											v-for="year in allYears"
											:key="year"
											:prop="year.toString()"
											:label="year.toString()"
											width="100"
											align="center"
										/>
									</el-table-column>
								</el-table>
							</div>
						</div>

						<!-- 税务计算 -->
						<div class="cash-flow-section">
							<h3>税务计算</h3>
							<div class="preview-table-container">
								<el-table :data="NetCashFlowData" border stripe class="CashFlow-table">
									<el-table-column prop="项目名称" label="项目名称" width="200" align="center" fixed="left" />
									<el-table-column prop="合计" label="合计" width="120" align="center" />
									<el-table-column label="建设期" align="center">
										<el-table-column prop="-2" label="-2" width="100" align="center" />
										<el-table-column prop="-1" label="-1" width="100" align="center" />
									</el-table-column>
									<el-table-column label="运营期(年)" align="center">
										<el-table-column
											v-for="year in allYears"
											:key="year"
											:prop="year.toString()"
											:label="year.toString()"
											width="100"
											align="center"
										/>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Refresh, Download, Close, Folder, ArrowDown } from '@element-plus/icons-vue';
import { useProjectStore } from '@/store/modules/project';
import { getProjectsList, getProjectByName } from '@/api/modules/projects';
import { adaptEnergyData, adaptSolutionData, adaptFullProjectData, adaptProjectDataFromStore } from '@/utils/dataAdapter';
import { adaptCashFlowData as adaptCashFlowDataFromUtil } from '@/utils/cashFlowAdapter';
import * as XLSX from 'xlsx';

const route = useRoute();
const projectStore = useProjectStore();

const loading = ref(false);
const downloading = ref(false);
const activeSheet = ref('energy');
const selectedProject = ref(null);
const projectHistory = ref([]); // 改为本地状态管理

// 动态数据状态
const currentEnergyData = ref(null);
const currentSolutionData = ref(null);
const currentCashFlowData = ref(null);

// 从pinia store获取当前项目数据
const currentProject = computed(() => projectStore.currentProject);

// 现金流数据（优先使用pinia store数据）
const CashFlowInflowData = computed(() => {
	if (currentCashFlowData.value?.cashInflow) {
		return currentCashFlowData.value.cashInflow;
	}

	// 如果没有适配后的数据，尝试从store直接适配
	if (currentProject.value?.cash_flow) {
		const adaptedData = adaptCashFlowDataFromUtil(currentProject.value.cash_flow);
		return adaptedData.cashInflow || [];
	}

	return [];
});

const CashFlowOutflowData = computed(() => {
	if (currentCashFlowData.value?.cashOutflow) {
		return currentCashFlowData.value.cashOutflow;
	}

	// 如果没有适配后的数据，尝试从store直接适配
	if (currentProject.value?.cash_flow) {
		const adaptedData = adaptCashFlowDataFromUtil(currentProject.value.cash_flow);
		return adaptedData.cashOutflow || [];
	}

	return [];
});

const NetCashFlowData = computed(() => {
	if (currentCashFlowData.value?.NetCashFlow) {
		return currentCashFlowData.value.NetCashFlow;
	}

	// 如果没有适配后的数据，尝试从store直接适配
	if (currentProject.value?.cash_flow) {
		const adaptedData = adaptCashFlowDataFromUtil(currentProject.value.cash_flow);
		return adaptedData.NetCashFlow || [];
	}

	return [];
});

// 加载项目历史记录
const loadProjectHistory = async () => {
	try {
		loading.value = true;
		const response = await getProjectsList({ skip: 0, limit: 100 });

		if (response.code === 200 && response.data) {
			// 转换后端数据格式为前端需要的格式，并按时间倒序排列
			projectHistory.value = response.data
				.map((project) => ({
					id: project.id || project.project_id,
					name: project.project_name || project.name,
					updatedAt: project.updated_at || project.updatedAt || new Date().toISOString(),
					createdAt: project.created_at || project.createdAt || new Date().toISOString(),
				}))
				.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)); // 时间倒序，最新的在前

			// 设置默认选中第一个项目（最新的项目）
			if (projectHistory.value.length > 0 && !selectedProject.value) {
				selectedProject.value = projectHistory.value[0];
				console.log('自动选择最新项目:', selectedProject.value);
			}

			console.log('项目历史记录加载成功，共', projectHistory.value.length, '个项目，按时间倒序排列');
		} else {
			console.warn('项目列表API返回格式异常，使用默认数据');
			// 如果API失败，使用store中的默认数据
			projectHistory.value = projectStore.projectHistory;
			if (projectHistory.value.length > 0 && !selectedProject.value) {
				selectedProject.value = projectHistory.value[0];
			}
		}
	} catch (error) {
		console.error('加载项目历史记录失败:', error);
		ElMessage.warning('项目历史记录加载失败，使用默认数据');
		// 降级使用store中的数据
		projectHistory.value = projectStore.projectHistory;
		if (projectHistory.value.length > 0 && !selectedProject.value) {
			selectedProject.value = projectHistory.value[0];
		}
	} finally {
		loading.value = false;
	}
};

// 根据项目名称加载项目详情
const loadProjectByName = async (projectName, projectId) => {
	try {
		loading.value = true;
		
		console.log('开始加载项目详情 - 项目名:', projectName, 'ID:', projectId);
		
		// 调用API根据项目名称获取详细数据
		const response = await getProjectByName({ project_name: projectName });
		
		if (response.code === 200 && response.data) {
			console.log('成功从API获取项目数据:', response.data);
			
			// 使用新的完整数据适配器
			const adaptedData = adaptFullProjectData(response.data);
			
			// 更新页面数据
			currentEnergyData.value = adaptedData.energyData;
			currentSolutionData.value = adaptedData.solutionData;
			currentCashFlowData.value = adaptedData.CashFlowData;
			
			console.log('数据适配完成:', {
				energyData: currentEnergyData.value,
				solutionData: currentSolutionData.value?.length,
				CashFlowData: Object.keys(currentCashFlowData.value || {}).length
			});
			
			ElMessage.success('项目数据加载成功');
		} else {
			console.warn('API返回格式异常，使用默认数据');
			loadDefaultData();
			ElMessage.warning('项目详情加载失败，使用默认数据');
		}
		
		return true;
	} catch (error) {
		console.error('加载项目详情失败:', error);
		loadDefaultData();
		ElMessage.warning('项目详情加载失败，使用默认数据');
		return false;
	} finally {
		loading.value = false;
	}
};

// 从pinia store加载项目数据
const loadProjectDataFromStore = () => {
	console.log('从pinia store加载项目数据:', currentProject.value);

	if (!currentProject.value) {
		console.warn('pinia store中没有当前项目数据，使用默认数据');
		loadDefaultData();
		return;
	}

	try {
		// 使用新的适配器函数处理pinia store数据
		const adaptedData = adaptProjectDataFromStore(currentProject.value);

		currentEnergyData.value = adaptedData.energyData;
		currentSolutionData.value = adaptedData.solutionData;
		currentCashFlowData.value = adaptedData.CashFlowData;

		console.log('pinia store数据适配完成:', {
			energyData: currentEnergyData.value ? '已加载' : '未加载',
			solutionData: currentSolutionData.value?.length || 0,
			CashFlowData: Object.keys(currentCashFlowData.value || {}).length
		});

		ElMessage.success('项目数据已从缓存加载');
	} catch (error) {
		console.error('从pinia store加载数据失败:', error);
		loadDefaultData();
		ElMessage.warning('数据加载失败，使用默认数据');
	}
};

// 加载默认数据
const loadDefaultData = () => {
	currentEnergyData.value = adaptEnergyData(null);
	currentSolutionData.value = adaptSolutionData(null);
	currentCashFlowData.value = {
		cashInflow: [],
		cashOutflow: [],
		NetCashFlow: []
	};
};

// 分别获取不同模块的数据（优先使用适配后的数据）
const photovoltaicData = computed(() => {
	if (currentEnergyData.value?.photovoltaicData?.length > 0) {
		return currentEnergyData.value.photovoltaicData;
	}
	return [];
});

const windPowerData = computed(() => {
	if (currentEnergyData.value?.windPowerData?.length > 0) {
		return currentEnergyData.value.windPowerData;
	}
	return [];
});

const gridSystemData = computed(() => {
	if (currentEnergyData.value?.gridSystemData?.length > 0) {
		return currentEnergyData.value.gridSystemData;
	}
	return [];
});

const hydrogenPlantData = computed(() => {
	if (currentEnergyData.value?.hydrogenPlantData?.length > 0) {
		return currentEnergyData.value.hydrogenPlantData;
	}
	return [];
});

const financingData = computed(() => {
	if (currentEnergyData.value?.financingData?.length > 0) {
		return currentEnergyData.value.financingData;
	}
	return [];
});

const liquidAmmoniaData = computed(() => {
	if (currentEnergyData.value?.liquidAmmoniaData?.length > 0) {
		return currentEnergyData.value.liquidAmmoniaData;
	}
	return [];
});

// 获取能源项目测算数据（合并所有模块）
const energyDataCombined = computed(() => {
	const combined = [
		...photovoltaicData.value,
		...windPowerData.value,
		...gridSystemData.value,
		...hydrogenPlantData.value,
		...financingData.value,
		...liquidAmmoniaData.value,
	];

	console.log('合并后的能源数据条数:', combined.length);
	return combined;
});

// 获取产品方案数据（优先使用适配后的数据）
const solutionData = computed(() => {
	if (currentSolutionData.value?.length > 0) {
		return currentSolutionData.value;
	}
	return [];
});
// 显示现金流数据中的年份列
const allYears = computed(() => {
	// 检查现金流数据中实际有的年份列
	const firstRow = CashFlowInflowData.value[0] || CashFlowOutflowData.value[0] || NetCashFlowData.value[0];
	if (!firstRow) {
		console.log('没有现金流数据，使用默认年份');
		return Array.from({ length: 25 }, (_, i) => i + 1);
	}

	// 提取所有正数年份列（运营期）
	const yearKeys = Object.keys(firstRow).filter((key) => /^\d+$/.test(key) && parseInt(key) > 0);
	const years = yearKeys.map((k) => parseInt(k)).sort((a, b) => a - b);

	console.log('现金流年份列表:', years);

	if (years.length === 0) {
		return Array.from({ length: 25 }, (_, i) => i + 1);
	}

	return years;
});

// 处理项目选择
const handleProjectSelect = async (projectId) => {
	const project = projectHistory.value.find((p) => p.id === projectId);
	if (project) {
		selectedProject.value = project;
		ElMessage.success(`正在切换到项目：${project.name}`);

		// 切换项目时重新加载项目数据
		await loadProjectByName(project.name, project.id);

		// 输出调试信息
		console.log('项目切换完成，当前数据状态:', {
			energyData: energyDataCombined.value.length,
			solutionData: solutionData.value.length,
			cashFlowInflow: CashFlowInflowData.value.length,
			cashFlowOutflow: CashFlowOutflowData.value.length,
			netCashFlow: NetCashFlowData.value.length
		});
	}
};

// 格式化时间
const formatTime = (timeStr) => {
	if (!timeStr) return '';
	const date = new Date(timeStr);
	return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
};

// 刷新预览数据（优先从pinia store，必要时从后端加载）
const refreshPreview = async () => {
	loading.value = true;
	try {
		console.log('刷新预览数据');

		// 首先尝试从pinia store加载数据
		if (currentProject.value) {
			console.log('从pinia store刷新数据');
			loadProjectDataFromStore();
			ElMessage.success('预览数据已刷新');
		} else if (selectedProject.value) {
			console.log('从后端API刷新数据 - 项目:', selectedProject.value?.name);
			// 如果pinia store没有数据，从后端加载
			await loadProjectByName(selectedProject.value.name, selectedProject.value.id);
			ElMessage.success(`预览数据已刷新 - ${selectedProject.value?.name}`);
		} else {
			ElMessage.warning('没有可用的项目数据');
		}
	} catch (error) {
		ElMessage.error('刷新数据失败');
		console.error(error);
	} finally {
		loading.value = false;
	}
};

// 监听pinia store中currentProject的变化
watch(currentProject, (newProject) => {
	if (newProject) {
		console.log('检测到pinia store项目数据变化，重新加载数据');
		loadProjectDataFromStore();
	}
}, { deep: true, immediate: false });

// 下载Excel文件
const downloadExcel = async () => {
	downloading.value = true;
	try {
		console.log('开始生成Excel文件');

		// 确保数据已加载
		if (!currentEnergyData.value && !currentSolutionData.value && !currentCashFlowData.value) {
			console.log('数据未加载，尝试从pinia store加载');
			loadProjectDataFromStore();
		}

		// 处理项目选择提示（不阻断下载）
		if (!selectedProject.value) {
			ElMessage.warning('未选择项目，将下载当前页面显示的数据');
		}

		// 创建新的工作簿
		const wb = XLSX.utils.book_new();

		// Sheet 1: 能源项目测算（包含光伏、风电、电网系统、制氢厂、融资贷款、液氨参数）
		console.log('生成能源项目测算sheet，数据条数:', energyDataCombined.value.length);
		const energySheet = XLSX.utils.json_to_sheet(energyDataCombined.value);
		energySheet['!cols'] = [
			{ width: 20 }, // 第1列（参数名称）
			{ width: 15 }, // 第2列（数值）
			{ width: 12 }, // 第3列（单位）
			{ width: 15 }, // 第4列（类别）
			{ width: 30 }, // 第5列（备注）
		];
		XLSX.utils.book_append_sheet(wb, energySheet, '能源项目测算');

		// Sheet 2: 产品方案（可再生能源综合制甲醇高效利用系统配置结果）
		console.log('生成产品方案sheet，数据条数:', solutionData.value.length);
		const solutionSheet = XLSX.utils.json_to_sheet(solutionData.value);
		solutionSheet['!cols'] = [
			{ width: 20 }, // 第1列（方案名称/参数）
			{ width: 15 }, // 第2列（数值）
			{ width: 12 }, // 第3列（单位）
			{ width: 15 }, // 第4列（类别）
			{ width: 30 }, // 第5列（备注）
		];
		XLSX.utils.book_append_sheet(wb, solutionSheet, '产品方案');

		// Sheet 3: 现金流量分析（包含现金流入、现金流出、税务计算）
		console.log('生成现金流量分析sheet');
		console.log('现金流入数据条数:', CashFlowInflowData.value.length);
		console.log('现金流出数据条数:', CashFlowOutflowData.value.length);
		console.log('税务计算数据条数:', NetCashFlowData.value.length);

		// 获取年份列表
		const getCashFlowYears = () => {
			const firstRow = CashFlowInflowData.value[0] || CashFlowOutflowData.value[0] || NetCashFlowData.value[0];
			if (!firstRow) {
				// 默认年份：建设期(-2、-1) + 运营期(1~25)
				return [-2, -1, ...Array.from({ length: 25 }, (_, i) => i + 1)];
			}
			// 提取所有数字列（包含负数）
			const yearKeys = Object.keys(firstRow).filter((key) => /^-?\d+$/.test(key));
			return yearKeys.map((k) => parseInt(k)).sort((a, b) => a - b); // 排序：-2, -1, 1, 2...25
		};

		const cashFlowYears = getCashFlowYears();
		console.log('现金流年份列表:', cashFlowYears);

		// 构建表头（两行）和合并规则
		const columns = ['项目名称', '合计', ...cashFlowYears.map((y) => y.toString())];
		const headerRow0 = []; // 第一行：分组表头（建设期、运营期）
		const headerRow1 = []; // 第二行：具体年份/字段
		const merges = []; // 合并单元格配置

		// 项目名称列（A列，索引0）
		headerRow0.push('项目名称');
		headerRow1.push('项目名称');

		// 合计列（B列，索引1）
		headerRow0.push('合计');
		headerRow1.push('合计');

		// 建设期列（C-D列：-2、-1，索引2、3）
		if (cashFlowYears.includes(-2) && cashFlowYears.includes(-1)) {
			headerRow0.push('建设期');
			headerRow1.push('-2');
			headerRow1.push('-1');
			merges.push({ s: { r: 0, c: 2 }, e: { r: 0, c: 3 } }); // 合并建设期列
		}

		// 运营期列（从第一个正数年份开始）
		const operationStartCol = headerRow1.length;
		const operationYears = cashFlowYears.filter((y) => y >= 1);
		if (operationYears.length > 0) {
			headerRow0.push('运营期');
			operationYears.forEach((y) => headerRow1.push(y.toString()));
			const operationEndCol = headerRow1.length - 1;
			merges.push({ s: { r: 0, c: operationStartCol }, e: { r: 0, c: operationEndCol } }); // 合并运营期列
		}

		// 构建数据行（按板块：流入、流出、税务）
		const rows = [headerRow0, headerRow1];
		let currentRow = 2; // 数据从第3行开始（索引2）

		const sections = [
			{ title: '现金流入', data: CashFlowInflowData.value },
			{ title: '现金流出', data: CashFlowOutflowData.value },
			{ title: '税务计算', data: NetCashFlowData.value },
		];

		sections.forEach((section) => {
			const { title, data } = section;
			if (data.length === 0) {
				console.log(`跳过空的${title}数据`);
				return;
			}

			// 添加分组标题行（如“现金流入”）
			const titleRow = new Array(columns.length).fill('');
			titleRow[0] = title; // 标题居左，跨列合并
			rows.push(titleRow);
			merges.push({
				s: { r: currentRow, c: 0 },
				e: { r: currentRow, c: columns.length - 1 },
			}); // 合并整行
			currentRow++;

			// 添加数据行（按columns顺序映射）
			data.forEach((item) => {
				const dataRow = columns.map((key) => item[key] ?? ''); // 空值处理
				rows.push(dataRow);
				currentRow++;
			});

			// 添加空行分隔（最后一个板块除外）
			if (section !== sections.at(-1)) {
				rows.push(new Array(columns.length).fill(''));
				currentRow++;
			}
		});

		// 创建工作表并配置格式
		const cashFlowSheet = XLSX.utils.aoa_to_sheet(rows);
		cashFlowSheet['!merges'] = merges; // 应用合并规则
		cashFlowSheet['!cols'] = [
			{ width: 28 }, // 项目名称（宽列）
			{ width: 12 }, // 合计
			...cashFlowYears.map(() => ({ width: 10 })), // 年份列统一宽度
		];

		XLSX.utils.book_append_sheet(wb, cashFlowSheet, '现金流量分析');

		// 生成Excel文件
		console.log('开始生成Excel文件');
		const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
		const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

		// 创建下载链接
		const url = window.URL.createObjectURL(data);
		const link = document.createElement('a');
		link.href = url;

		// 处理项目名称可能为空的情况，使用默认值
		const projectName = selectedProject.value?.name || currentProject.value?.project_info?.name || '当前页面数据';
		const timestamp = new Date().toISOString().slice(0, 10);
		link.download = `导出报告_${projectName}_${timestamp}.xlsx`;

		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		window.URL.revokeObjectURL(url);

		console.log('Excel文件下载完成:', link.download);
		ElMessage.success('Excel文件下载成功');
	} catch (error) {
		console.error('下载失败:', error);
		ElMessage.error('Excel文件生成失败，请检查数据格式');
	} finally {
		downloading.value = false;
	}
};

// 关闭窗口
const closeWindow = () => {
	window.close();
};

	// 页面初始化
onMounted(async () => {
	console.log('导出预览页面开始初始化');

	// 首先检查pinia store中是否有当前项目数据
	if (currentProject.value) {
		console.log('发现pinia store中有项目数据，直接使用');
		loadProjectDataFromStore();

		// 设置选中的项目信息
		if (currentProject.value.project_info) {
			selectedProject.value = {
				id: currentProject.value.project_info.id,
				name: currentProject.value.project_info.name,
				updatedAt: currentProject.value.project_info.updated_at,
				createdAt: currentProject.value.project_info.created_at
			};
		}
	} else {
		console.log('pinia store中没有项目数据，初始化默认数据');
		loadDefaultData();
	}

	// 初始化 store 的默认数据
	if (typeof projectStore.initDefaultHistory === 'function') {
		projectStore.initDefaultHistory();
	}

	// 加载项目历史记录
	await loadProjectHistory();

	const projectId = route.query.projectId;
	if (projectId && !currentProject.value) {
		// 只有在pinia store没有数据时才从API加载
		console.log('根据URL参数加载项目数据，项目ID:', projectId);
		const project = projectHistory.value.find((p) => p.id == projectId);
		if (project) {
			selectedProject.value = project;
			await loadProjectByName(project.name, project.id);
		} else if (projectHistory.value.length > 0) {
			// 如果找不到对应项目，使用第一个项目（最新的）
			selectedProject.value = projectHistory.value[0];
			await loadProjectByName(selectedProject.value.name, selectedProject.value.id);
		}
	} else if (!selectedProject.value && projectHistory.value.length > 0) {
		// 如果没有选中项目且有历史记录，选择最新的
		selectedProject.value = projectHistory.value[0];
		if (!currentProject.value) {
			await loadProjectByName(selectedProject.value.name, selectedProject.value.id);
		}
	}

	// 如果还是没有项目，创建临时项目名称
	if (!selectedProject.value) {
		const now = new Date();
		const tempProjectName = `当前数据_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
		selectedProject.value = {
			id: 'temp',
			name: tempProjectName,
			createdAt: now.toISOString(),
			updatedAt: now.toISOString()
		};
	}

	console.log('导出预览页面初始化完成');
	console.log('当前选中项目:', selectedProject.value);
	console.log('pinia store项目数据:', currentProject.value ? '已加载' : '未加载');
	console.log('当前数据状态:', {
		energyData: currentEnergyData.value ? '已加载' : '未加载',
		solutionData: currentSolutionData.value ? '已加载' : '未加载',
		CashFlowData: currentCashFlowData.value ? '已加载' : '未加载'
	});
});
</script>

<style scoped>
.export-preview-page {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding: 20px;
}

.preview-header {
	background: white;
	padding: 20px;
	border-radius: 8px;
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 20px;
}

.preview-header h1 {
	margin: 0;
	color: #303133;
	font-size: 24px;
}

.project-selector {
	display: flex;
	align-items: center;
}

.project-item {
	display: flex;
	flex-direction: column;
	min-width: 250px;
}

.project-name {
	font-weight: 500;
	color: #303133;
	margin-bottom: 2px;
	font-size: 14px;
}

.project-time {
	font-size: 12px;
	color: #909399;
}

:deep(.el-dropdown-menu__item.is-active .project-name) {
	color: #409eff;
}

.header-actions {
	display: flex;
	gap: 12px;
}

.preview-content {
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	min-height: 600px;
}

.sheet-tabs {
	padding: 20px;
}

.sheet-content {
	padding: 20px 0;
}

.sheet-content h3 {
	margin: 0 0 20px 0;
	color: #303133;
	font-size: 18px;
	font-weight: 600;
}

.preview-table-container {
	max-height: 650px;
	overflow: auto;
	border: 1px solid #ebeef5;
	border-radius: 4px;
}


.CashFlow-table {
	min-height: 200px;
	font-size: 12px;
}

.cash-flow-section {
	margin-bottom: 30px;
}

:deep(.el-table th) {
	background-color: #f8f9fa;
	color: #303133;
	font-weight: 600;
}

@media (max-width: 768px) {
	.preview-header {
		flex-direction: column;
		gap: 16px;
		text-align: center;
	}

	.header-left {
		flex-direction: column;
		gap: 12px;
		width: 100%;
	}

	.header-actions {
		flex-wrap: wrap;
		justify-content: center;
	}
}
</style>
