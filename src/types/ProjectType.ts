interface ProjectInfo {
  id: number;
  name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

interface OperatingParams {
  id: number;
  project_id: number;
  pv_capacity_mw: number;
  pv_utilization_hours: number;
  pv_degradation_y1_percent: number;
  pv_degradation_ongoing_percent: number;
  pv_system_efficiency_percent: number;
  pv_transmission_distance_km: number;
  pv_equipment_depreciation_years: number;
  pv_grid_price_tax_included_yuan_per_kwh: number;
  wind_capacity_mw: number;
  wind_utilization_hours: number;
  wind_system_efficiency_percent: number;
  wind_transmission_distance_km: number;
  wind_equipment_depreciation_years: number;
  grid_price_tax_included_yuan_per_kwh: number;
  grid_transmission_length_km: number;
  grid_transmission_unit_cost_wan_yuan_per_km: number;
  grid_step_up_station_capacity_mva: number;
  grid_step_up_station_unit_cost_wan_yuan_per_mva: number;
  grid_step_down_station_capacity_mva: number;
  grid_step_down_station_unit_cost_wan_yuan_per_mva: number;
  h2_plant_capacity_mw: number;
  h2_consumption_kwh_per_nm3: number;
  h2_base_consumption_kwh_per_kg: number;
  h2_equipment_service_years: number;
  h2_consumption_increase_annual: number;
  h2_water_price_yuan_per_ton: number;
  h2_wastewater_price_yuan_per_ton: number;
  h2_staff_count: number;
  h2_staff_salary_wan_yuan_per_year: number;
  h2_price_transport: number;
  h2_price_chemical: number;
  h2_storage_investment_wan_yuan: number;
  h2_storage_capacity_tons: number;
  h2_equipment_depreciation_years: number;
  o2_price_per_ton: number;
  loan_term_years: number;
  loan_interest_rate: number;
  finance_land_rent_wan_yuan_per_year: number;
  loan_ratio: number;
  finance_loan_total_wan_yuan: number;
  ammonia_price_yuan_per_ton: number;
  ammonia_consumption_tons_per_hour: number;
  electrochemical_energy_storage_scale_mw: number;
  created_at: string;
  updated_at: string;
}

interface FixedInvestment {
  id: number;
  project_id: number;
  item_name: string;
  scale: number;
  scale_unit: string | null; // Can be "MW", "km", "t", or null for total row
  unit_cost: number;
  unit_cost_unit: string | null; // Can be "万元/MW", "万元/km", "万元/t", or null for total row
  offset: number;
  total_investment: number;
  is_total_row: boolean;
  created_at: string;
  updated_at: string;
}

interface EnergyMaterialBalanceItem {
  pv_attenuation_coefficient: number;
  pv_efficiency: number;
  wind_efficiency: number;
  grid_sales_ratio: number;
  h2_consumption_increase: number;
  h2_consumption_factor: number;
  pv_generation_mwh: number;
  wind_generation_mwh: number;
  total_generation_mwh: number;
  grid_sales_mwh: number;
  pv_grid_sales_mwh: number;
  wind_grid_sales_mwh: number;
  loss_mwh: number;
  h2_electricity_mwh: number;
  h2_production_tons: number;
  h2_sales_transport_tons: number;
  h2_sales_chemical_tons: number;
  o2_production_tons: number;
  project_id: number;
  year: number;
  calculated_at: string;
  id: number;
  created_at: string;
  updated_at: string;
}

interface CashFlowItem {
  h2_transport_sales_income: number;
  h2_chemical_sales_income: number;
  o2_sales_income: number;
  ammonia_sales_income: number;
  steam_sales_income: number;
  pv_electricity_sales_income: number;
  wind_electricity_sales_income: number;
  other_income: number;
  pv_station_residual_value: number;
  wind_station_residual_value: number;
  pv_transmission_residual_value: number;
  wind_transmission_residual_value: number;
  step_up_station_residual_value: number;
  step_down_station_residual_value: number;
  battery_storage_residual_value: number;
  h2_plant_residual_value: number;
  h2_storage_residual_value: number;
  h2_auxiliary_residual_value: number;
  synthesis_equipment_residual_value: number;
  other_equipment_residual_value: number;
  government_subsidy: number;
  total_cash_inflow: number;
  pv_station_investment: number;
  wind_station_investment: number;
  pv_transmission_investment: number;
  wind_transmission_investment: number;
  step_up_station_investment: number;
  step_down_station_investment: number;
  battery_storage_investment: number;
  h2_plant_investment: number;
  h2_storage_investment: number;
  h2_auxiliary_investment: number;
  synthesis_equipment_investment: number;
  other_equipment_investment: number;
  h2_raw_materials_cost: number;
  h2_maintenance_cost: number;
  h2_personnel_cost: number;
  h2_other_operating_cost: number;
  h2_water_cost: number;
  h2_wastewater_cost: number;
  h2_insurance_cost: number;
  pv_operating_cost: number;
  pv_insurance_cost: number;
  pv_financial_cost: number;
  wind_operating_cost: number;
  wind_insurance_cost: number;
  wind_financial_cost: number;
  total_cash_outflow: number;
  net_cash_flow_before_tax: number;
  cumulative_net_cash_flow_before_tax: number;
  depreciation: number;
  vat_output: number;
  vat_input_operating: number;
  vat_input_fixed_assets: number;
  vat_payable: number;
  vat_surcharge: number;
  total_vat_and_surcharge: number;
  income_tax: number;
  net_cash_flow_after_tax: number;
  cumulative_net_cash_flow_after_tax: number;
  project_id: number;
  year: number;
  calculated_at: string;
  id: number;
  created_at: string;
  updated_at: string;
}

interface Project {
  project_info: ProjectInfo;
  operating_params: OperatingParams;
  fixed_investments: FixedInvestment[];
  energy_material_balance: EnergyMaterialBalanceItem[];
  cash_flow: CashFlowItem[];
}