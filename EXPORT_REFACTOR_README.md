# 导出预览页面重构说明

## 重构概述

本次重构主要针对 `src/views/ExportPreview.vue` 页面，目标是使其能够正确从 pinia store 中获取 `currentProject` 数据，并导出包含3个sheet的Excel文件。

## 主要改进

### 1. 数据获取逻辑重构

**之前的问题：**
- 主要依赖API调用获取数据
- 没有充分利用pinia store中的currentProject数据
- 缺少对store数据变化的监听

**重构后的改进：**
- 优先从pinia store的currentProject获取数据
- 添加了对store数据变化的监听
- API调用作为备用方案，当store中没有数据时才使用
- 新增 `loadProjectDataFromStore()` 函数专门处理store数据

### 2. 数据适配器完善

**新增功能：**
- 添加了 `adaptProjectDataFromStore()` 函数，专门处理pinia store数据
- 完善了产品方案数据的适配逻辑，支持从运营参数和能源物质平衡数据生成默认方案
- 改进了现金流数据的适配，避免循环依赖问题
- 增强了错误处理和数据验证

**文件变更：**
- `src/utils/dataAdapter.js` - 新增store数据适配函数
- `src/utils/testDataAdapter.js` - 新增测试文件

### 3. Excel导出功能优化

**改进内容：**
- 确保数据来源正确（优先使用store数据）
- 改进现金流表格的年份列处理
- 增加详细的调试日志
- 优化表格格式和列宽设置
- 改进文件命名逻辑

### 4. 页面预览显示优化

**优化内容：**
- 改进各模块数据的计算逻辑
- 添加数据状态检查
- 优化现金流年份列的显示
- 增加调试信息输出

## 数据流程

```
1. 页面初始化
   ↓
2. 检查pinia store中的currentProject
   ↓
3. 如果有数据 → loadProjectDataFromStore()
   如果没有数据 → loadDefaultData() + 从API加载
   ↓
4. 使用adaptProjectDataFromStore()适配数据
   ↓
5. 更新页面显示的computed属性
   ↓
6. 用户点击导出 → 生成Excel文件
```

## 支持的数据类型

### Sheet 1: 能源项目测算
- 光伏参数 (来自 operating_params)
- 风电参数 (来自 operating_params)
- 电网系统参数 (来自 operating_params)
- 制氢厂参数 (来自 operating_params)
- 融资贷款参数 (来自 operating_params)
- 液氨参数 (来自 operating_params)

### Sheet 2: 产品方案
- 固定投资数据 (来自 fixed_investments)
- 容量优化配置结果 (来自 operating_params)
- 总体产品方案 (来自 energy_material_balance)

### Sheet 3: 现金流量分析
- 现金流入 (来自 cash_flow)
- 现金流出 (来自 cash_flow)
- 税务计算 (来自 cash_flow)

## 使用方法

1. 确保pinia store中有currentProject数据
2. 点击header.vue中的"导出报告"按钮
3. 在导出预览页面中查看数据
4. 点击"下载Excel文件"按钮导出

## 测试

可以使用 `src/utils/testDataAdapter.js` 中的测试函数验证数据适配功能：

```javascript
// 在浏览器控制台中运行
testDataAdapter()
```

## 注意事项

1. 确保pinia store中的currentProject数据结构符合ProjectType.ts定义
2. 如果现金流数据为空，会显示空表格
3. 导出的Excel文件名包含项目名称和日期
4. 页面会自动监听store数据变化并更新显示

## 兼容性

- 保持了与原有API的兼容性
- 支持从API和store两种数据源
- 向后兼容原有的数据格式
